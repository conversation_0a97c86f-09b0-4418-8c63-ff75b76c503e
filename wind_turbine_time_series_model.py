#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
风机功率时间序列预测模型
处理异常值（功率<0）并训练时间序列模型
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.ensemble import RandomForestRegressor
from sklearn.linear_model import LinearRegression
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class WindTurbineTimeSeriesModel:
    def __init__(self, data_path):
        """
        初始化风机时间序列模型
        
        Args:
            data_path: 数据文件路径
        """
        self.data_path = data_path
        self.df = None
        self.df_clean = None
        self.scaler = StandardScaler()
        self.model = None
        
    def load_data(self):
        """加载数据"""
        print("正在加载数据...")
        self.df = pd.read_csv(self.data_path, encoding='gbk')
        print(f"数据形状: {self.df.shape}")
        print(f"列名: {self.df.columns.tolist()}")
        return self.df
    
    def explore_data(self):
        """数据探索"""
        print("\n=== 数据探索 ===")
        print("数据基本信息:")
        print(self.df.info())
        
        print("\n数据统计描述:")
        print(self.df.describe())
        
        # 检查异常值
        power_col = '功率(kw)'
        negative_power = self.df[power_col] < 0
        print(f"\n功率小于0的异常值数量: {negative_power.sum()}")
        print(f"异常值比例: {negative_power.mean():.4f}")
        print(f"异常值范围: {self.df[power_col][negative_power].min():.2f} ~ {self.df[power_col][negative_power].max():.2f}")
        
        return negative_power.sum()
    
    def clean_data(self):
        """数据清洗 - 处理异常值"""
        print("\n=== 数据清洗 ===")
        
        # 复制数据
        self.df_clean = self.df.copy()
        
        # 转换时间列
        self.df_clean['采样时刻'] = pd.to_datetime(self.df_clean['采样时刻'])
        self.df_clean.set_index('采样时刻', inplace=True)
        
        # 处理功率异常值（小于0的值）
        power_col = '功率(kw)'
        before_count = len(self.df_clean)
        
        # 方法1: 删除异常值
        # self.df_clean = self.df_clean[self.df_clean[power_col] >= 0]
        
        # 方法2: 用插值法填充异常值
        anomaly_mask = self.df_clean[power_col] < 0
        print(f"发现 {anomaly_mask.sum()} 个异常值")
        
        # 将异常值设为NaN，然后用线性插值填充
        self.df_clean.loc[anomaly_mask, power_col] = np.nan
        self.df_clean[power_col] = self.df_clean[power_col].interpolate(method='linear')
        
        # 如果首尾有NaN，用前向/后向填充
        self.df_clean[power_col] = self.df_clean[power_col].fillna(method='ffill').fillna(method='bfill')
        
        after_count = len(self.df_clean)
        print(f"清洗后数据点数量: {after_count}")
        print(f"功率范围: {self.df_clean[power_col].min():.2f} ~ {self.df_clean[power_col].max():.2f}")
        
        return self.df_clean
    
    def feature_engineering(self):
        """特征工程"""
        print("\n=== 特征工程 ===")
        
        # 时间特征
        self.df_clean['hour'] = self.df_clean.index.hour
        self.df_clean['day'] = self.df_clean.index.day
        self.df_clean['month'] = self.df_clean.index.month
        self.df_clean['dayofweek'] = self.df_clean.index.dayofweek
        
        # 滞后特征
        for lag in [1, 2, 3, 6, 12]:
            self.df_clean[f'功率_lag_{lag}'] = self.df_clean['功率(kw)'].shift(lag)
            self.df_clean[f'风速_lag_{lag}'] = self.df_clean['风速(m/s)'].shift(lag)
        
        # 滑动窗口特征
        for window in [3, 6, 12]:
            self.df_clean[f'功率_rolling_mean_{window}'] = self.df_clean['功率(kw)'].rolling(window=window).mean()
            self.df_clean[f'功率_rolling_std_{window}'] = self.df_clean['功率(kw)'].rolling(window=window).std()
            self.df_clean[f'风速_rolling_mean_{window}'] = self.df_clean['风速(m/s)'].rolling(window=window).mean()
        
        # 删除包含NaN的行
        self.df_clean = self.df_clean.dropna()
        
        print(f"特征工程后数据形状: {self.df_clean.shape}")
        print(f"特征列: {self.df_clean.columns.tolist()}")
        
        return self.df_clean
    
    def prepare_data_for_training(self):
        """准备训练数据"""
        print("\n=== 准备训练数据 ===")
        
        # 目标变量
        target = '功率(kw)'
        
        # 特征变量（排除目标变量和发电量）
        feature_cols = [col for col in self.df_clean.columns 
                       if col not in [target, ' 发电量(kwh)']]
        
        X = self.df_clean[feature_cols]
        y = self.df_clean[target]
        
        print(f"特征数量: {len(feature_cols)}")
        print(f"样本数量: {len(X)}")
        
        # 划分训练集和测试集（时间序列按时间顺序划分）
        split_idx = int(len(X) * 0.8)
        
        X_train = X.iloc[:split_idx]
        X_test = X.iloc[split_idx:]
        y_train = y.iloc[:split_idx]
        y_test = y.iloc[split_idx:]
        
        # 标准化特征
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_test_scaled = self.scaler.transform(X_test)
        
        print(f"训练集大小: {X_train.shape}")
        print(f"测试集大小: {X_test.shape}")
        
        return X_train_scaled, X_test_scaled, y_train, y_test, feature_cols
    
    def train_model(self, X_train, y_train):
        """训练模型"""
        print("\n=== 训练模型 ===")
        
        # 使用随机森林回归器
        self.model = RandomForestRegressor(
            n_estimators=100,
            max_depth=10,
            random_state=42,
            n_jobs=-1
        )
        
        print("正在训练随机森林模型...")
        self.model.fit(X_train, y_train)
        print("模型训练完成!")
        
        return self.model
    
    def evaluate_model(self, X_test, y_test):
        """评估模型"""
        print("\n=== 模型评估 ===")
        
        # 预测
        y_pred = self.model.predict(X_test)
        
        # 计算评估指标
        mse = mean_squared_error(y_test, y_pred)
        rmse = np.sqrt(mse)
        mae = mean_absolute_error(y_test, y_pred)
        r2 = r2_score(y_test, y_pred)
        
        print(f"均方误差 (MSE): {mse:.2f}")
        print(f"均方根误差 (RMSE): {rmse:.2f}")
        print(f"平均绝对误差 (MAE): {mae:.2f}")
        print(f"决定系数 (R²): {r2:.4f}")
        
        return y_pred, {'mse': mse, 'rmse': rmse, 'mae': mae, 'r2': r2}
    
    def plot_results(self, y_test, y_pred):
        """绘制结果"""
        print("\n=== 绘制结果 ===")
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 预测vs实际值散点图
        axes[0, 0].scatter(y_test, y_pred, alpha=0.6)
        axes[0, 0].plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--', lw=2)
        axes[0, 0].set_xlabel('实际功率 (kW)')
        axes[0, 0].set_ylabel('预测功率 (kW)')
        axes[0, 0].set_title('预测值 vs 实际值')
        
        # 时间序列对比图
        test_index = y_test.index[-200:]  # 显示最后200个点
        axes[0, 1].plot(test_index, y_test.loc[test_index], label='实际值', alpha=0.7)
        axes[0, 1].plot(test_index, y_pred[-200:], label='预测值', alpha=0.7)
        axes[0, 1].set_xlabel('时间')
        axes[0, 1].set_ylabel('功率 (kW)')
        axes[0, 1].set_title('时间序列预测对比（最后200个点）')
        axes[0, 1].legend()
        axes[0, 1].tick_params(axis='x', rotation=45)
        
        # 残差图
        residuals = y_test - y_pred
        axes[1, 0].scatter(y_pred, residuals, alpha=0.6)
        axes[1, 0].axhline(y=0, color='r', linestyle='--')
        axes[1, 0].set_xlabel('预测功率 (kW)')
        axes[1, 0].set_ylabel('残差')
        axes[1, 0].set_title('残差图')
        
        # 残差分布直方图
        axes[1, 1].hist(residuals, bins=50, alpha=0.7, edgecolor='black')
        axes[1, 1].set_xlabel('残差')
        axes[1, 1].set_ylabel('频次')
        axes[1, 1].set_title('残差分布')
        
        plt.tight_layout()
        plt.savefig('wind_turbine_prediction_results.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        return fig
    
    def get_feature_importance(self, feature_cols):
        """获取特征重要性"""
        if hasattr(self.model, 'feature_importances_'):
            importance_df = pd.DataFrame({
                'feature': feature_cols,
                'importance': self.model.feature_importances_
            }).sort_values('importance', ascending=False)
            
            print("\n=== 特征重要性 ===")
            print(importance_df.head(10))
            
            # 绘制特征重要性图
            plt.figure(figsize=(10, 6))
            sns.barplot(data=importance_df.head(10), x='importance', y='feature')
            plt.title('前10个最重要特征')
            plt.xlabel('重要性')
            plt.tight_layout()
            plt.savefig('feature_importance.png', dpi=300, bbox_inches='tight')
            plt.show()
            
            return importance_df
        else:
            print("模型不支持特征重要性分析")
            return None

def main():
    """主函数"""
    print("=== 风机功率时间序列预测模型 ===")
    
    # 初始化模型
    model = WindTurbineTimeSeriesModel('2021年1月1期01风机数据.csv')
    
    # 加载和探索数据
    model.load_data()
    model.explore_data()
    
    # 数据清洗和特征工程
    model.clean_data()
    model.feature_engineering()
    
    # 准备训练数据
    X_train, X_test, y_train, y_test, feature_cols = model.prepare_data_for_training()
    
    # 训练模型
    model.train_model(X_train, y_train)
    
    # 评估模型
    y_pred, metrics = model.evaluate_model(X_test, y_test)
    
    # 绘制结果
    model.plot_results(y_test, y_pred)
    
    # 特征重要性分析
    model.get_feature_importance(feature_cols)
    
    print("\n=== 模型训练完成 ===")
    print("结果图片已保存为: wind_turbine_prediction_results.png 和 feature_importance.png")

if __name__ == "__main__":
    main()
