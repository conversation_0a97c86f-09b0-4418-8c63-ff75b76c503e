#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
基于LSTM的风机功率时间序列预测模型
处理异常值（功率<0）并使用深度学习进行预测
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

try:
    import tensorflow as tf
    from tensorflow.keras.models import Sequential
    from tensorflow.keras.layers import LSTM, Dense, Dropout
    from tensorflow.keras.optimizers import Adam
    TENSORFLOW_AVAILABLE = True
except ImportError:
    print("TensorFlow未安装，将使用传统机器学习方法")
    TENSORFLOW_AVAILABLE = False

class LSTMWindTurbineModel:
    def __init__(self, data_path, sequence_length=24):
        """
        初始化LSTM风机预测模型
        
        Args:
            data_path: 数据文件路径
            sequence_length: 序列长度（用于预测的历史数据点数）
        """
        self.data_path = data_path
        self.sequence_length = sequence_length
        self.df = None
        self.df_clean = None
        self.scaler = MinMaxScaler()
        self.model = None
        
    def load_and_clean_data(self):
        """加载和清洗数据"""
        print("正在加载数据...")
        self.df = pd.read_csv(self.data_path, encoding='gbk')
        print(f"原始数据形状: {self.df.shape}")
        
        # 复制数据进行清洗
        self.df_clean = self.df.copy()
        
        # 转换时间列
        self.df_clean['采样时刻'] = pd.to_datetime(self.df_clean['采样时刻'])
        self.df_clean.set_index('采样时刻', inplace=True)
        
        # 处理功率异常值（小于0的值）
        power_col = '功率(kw)'
        anomaly_mask = self.df_clean[power_col] < 0
        anomaly_count = anomaly_mask.sum()
        
        print(f"发现 {anomaly_count} 个异常值（功率<0）")
        print(f"异常值比例: {anomaly_count/len(self.df_clean):.4f}")
        
        # 用线性插值替换异常值
        self.df_clean.loc[anomaly_mask, power_col] = np.nan
        self.df_clean[power_col] = self.df_clean[power_col].interpolate(method='linear')
        self.df_clean[power_col] = self.df_clean[power_col].fillna(method='ffill').fillna(method='bfill')
        
        print(f"清洗后功率范围: {self.df_clean[power_col].min():.2f} ~ {self.df_clean[power_col].max():.2f}")
        
        return self.df_clean
    
    def create_sequences(self, data, target_col):
        """创建时间序列序列"""
        sequences = []
        targets = []
        
        for i in range(self.sequence_length, len(data)):
            # 使用多个特征作为输入
            seq = data.iloc[i-self.sequence_length:i][['风速(m/s)', '功率(kw)', '发电机转速(rpm)', '叶片1角度(°)']].values
            target = data.iloc[i][target_col]
            
            sequences.append(seq)
            targets.append(target)
        
        return np.array(sequences), np.array(targets)
    
    def prepare_data(self):
        """准备训练数据"""
        print("\n=== 准备数据 ===")
        
        # 选择相关特征
        feature_cols = ['风速(m/s)', '功率(kw)', '发电机转速(rpm)', '叶片1角度(°)']
        data = self.df_clean[feature_cols].copy()
        
        # 数据标准化
        data_scaled = self.scaler.fit_transform(data)
        data_scaled_df = pd.DataFrame(data_scaled, columns=feature_cols, index=data.index)
        
        # 创建序列
        X, y = self.create_sequences(data_scaled_df, '功率(kw)')
        
        # 划分训练集和测试集
        split_idx = int(len(X) * 0.8)
        
        X_train = X[:split_idx]
        X_test = X[split_idx:]
        y_train = y[:split_idx]
        y_test = y[split_idx:]
        
        print(f"训练集形状: X_train: {X_train.shape}, y_train: {y_train.shape}")
        print(f"测试集形状: X_test: {X_test.shape}, y_test: {y_test.shape}")
        
        return X_train, X_test, y_train, y_test
    
    def build_lstm_model(self, input_shape):
        """构建LSTM模型"""
        if not TENSORFLOW_AVAILABLE:
            raise ImportError("需要安装TensorFlow才能使用LSTM模型")
        
        model = Sequential([
            LSTM(50, return_sequences=True, input_shape=input_shape),
            Dropout(0.2),
            LSTM(50, return_sequences=False),
            Dropout(0.2),
            Dense(25),
            Dense(1)
        ])
        
        model.compile(optimizer=Adam(learning_rate=0.001), loss='mse', metrics=['mae'])
        
        return model
    
    def train_lstm_model(self, X_train, y_train, X_test, y_test):
        """训练LSTM模型"""
        print("\n=== 训练LSTM模型 ===")
        
        if not TENSORFLOW_AVAILABLE:
            print("TensorFlow未安装，使用简单的线性回归模型替代")
            return self.train_simple_model(X_train, y_train)
        
        # 构建模型
        self.model = self.build_lstm_model((X_train.shape[1], X_train.shape[2]))
        
        print("模型结构:")
        self.model.summary()
        
        # 训练模型
        history = self.model.fit(
            X_train, y_train,
            batch_size=32,
            epochs=50,
            validation_data=(X_test, y_test),
            verbose=1,
            shuffle=False
        )
        
        return history
    
    def train_simple_model(self, X_train, y_train):
        """训练简单模型（当TensorFlow不可用时）"""
        from sklearn.ensemble import RandomForestRegressor
        
        # 将3D数据展平为2D
        X_train_flat = X_train.reshape(X_train.shape[0], -1)
        
        self.model = RandomForestRegressor(n_estimators=100, random_state=42)
        self.model.fit(X_train_flat, y_train)
        
        print("使用随机森林模型训练完成")
        return None
    
    def predict(self, X_test):
        """进行预测"""
        if TENSORFLOW_AVAILABLE and hasattr(self.model, 'predict'):
            return self.model.predict(X_test).flatten()
        else:
            # 简单模型预测
            X_test_flat = X_test.reshape(X_test.shape[0], -1)
            return self.model.predict(X_test_flat)
    
    def inverse_transform_predictions(self, predictions, original_data):
        """反标准化预测结果"""
        # 创建一个与原始特征相同形状的数组
        dummy_data = np.zeros((len(predictions), 4))
        dummy_data[:, 1] = predictions  # 功率在第2列（索引1）
        
        # 反标准化
        inverse_scaled = self.scaler.inverse_transform(dummy_data)
        return inverse_scaled[:, 1]  # 返回功率列
    
    def evaluate_model(self, X_test, y_test, original_data):
        """评估模型"""
        print("\n=== 模型评估 ===")
        
        # 预测
        y_pred_scaled = self.predict(X_test)
        
        # 反标准化
        y_test_original = self.inverse_transform_predictions(y_test, original_data)
        y_pred_original = self.inverse_transform_predictions(y_pred_scaled, original_data)
        
        # 计算评估指标
        mse = mean_squared_error(y_test_original, y_pred_original)
        rmse = np.sqrt(mse)
        mae = mean_absolute_error(y_test_original, y_pred_original)
        r2 = r2_score(y_test_original, y_pred_original)
        
        print(f"均方误差 (MSE): {mse:.2f}")
        print(f"均方根误差 (RMSE): {rmse:.2f}")
        print(f"平均绝对误差 (MAE): {mae:.2f}")
        print(f"决定系数 (R²): {r2:.4f}")
        
        return y_pred_original, y_test_original, {'mse': mse, 'rmse': rmse, 'mae': mae, 'r2': r2}
    
    def plot_results(self, y_test, y_pred, test_dates):
        """绘制结果"""
        print("\n=== 绘制结果 ===")
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 预测vs实际值散点图
        axes[0, 0].scatter(y_test, y_pred, alpha=0.6)
        axes[0, 0].plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--', lw=2)
        axes[0, 0].set_xlabel('实际功率 (kW)')
        axes[0, 0].set_ylabel('预测功率 (kW)')
        axes[0, 0].set_title('预测值 vs 实际值')
        
        # 时间序列对比图（显示最后200个点）
        n_show = min(200, len(y_test))
        axes[0, 1].plot(range(n_show), y_test[-n_show:], label='实际值', alpha=0.7)
        axes[0, 1].plot(range(n_show), y_pred[-n_show:], label='预测值', alpha=0.7)
        axes[0, 1].set_xlabel('时间步')
        axes[0, 1].set_ylabel('功率 (kW)')
        axes[0, 1].set_title(f'时间序列预测对比（最后{n_show}个点）')
        axes[0, 1].legend()
        
        # 残差图
        residuals = y_test - y_pred
        axes[1, 0].scatter(y_pred, residuals, alpha=0.6)
        axes[1, 0].axhline(y=0, color='r', linestyle='--')
        axes[1, 0].set_xlabel('预测功率 (kW)')
        axes[1, 0].set_ylabel('残差')
        axes[1, 0].set_title('残差图')
        
        # 残差分布直方图
        axes[1, 1].hist(residuals, bins=50, alpha=0.7, edgecolor='black')
        axes[1, 1].set_xlabel('残差')
        axes[1, 1].set_ylabel('频次')
        axes[1, 1].set_title('残差分布')
        
        plt.tight_layout()
        plt.savefig('lstm_wind_turbine_results.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        return fig

def main():
    """主函数"""
    print("=== 基于LSTM的风机功率时间序列预测 ===")
    
    # 初始化模型
    model = LSTMWindTurbineModel('2021年1月1期01风机数据.csv', sequence_length=24)
    
    # 加载和清洗数据
    df_clean = model.load_and_clean_data()
    
    # 准备数据
    X_train, X_test, y_train, y_test = model.prepare_data()
    
    # 训练模型
    if TENSORFLOW_AVAILABLE:
        history = model.train_lstm_model(X_train, y_train, X_test, y_test)
    else:
        model.train_simple_model(X_train, y_train)
    
    # 评估模型
    y_pred, y_test_orig, metrics = model.evaluate_model(X_test, y_test, df_clean)
    
    # 获取测试集对应的日期
    test_start_idx = len(df_clean) - len(y_test) - model.sequence_length
    test_dates = df_clean.index[test_start_idx + model.sequence_length:]
    
    # 绘制结果
    model.plot_results(y_test_orig, y_pred, test_dates)
    
    print("\n=== 模型训练完成 ===")
    print("结果图片已保存为: lstm_wind_turbine_results.png")
    
    if TENSORFLOW_AVAILABLE:
        print("使用了LSTM深度学习模型")
    else:
        print("使用了随机森林模型（TensorFlow未安装）")

if __name__ == "__main__":
    main()
