#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
基于TensorFlow/Keras的风机功率时间序列预测模型
处理异常值（功率<0）并使用LSTM进行预测
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

# TensorFlow导入
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense, Dropout
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau

print(f"TensorFlow版本: {tf.__version__}")

def load_and_preprocess_data(file_path):
    """加载和预处理数据"""
    print("=== 数据加载和预处理 ===")
    
    # 加载数据
    df = pd.read_csv(file_path, encoding='gbk')
    print(f"原始数据形状: {df.shape}")
    
    # 转换时间列
    df['采样时刻'] = pd.to_datetime(df['采样时刻'])
    df.set_index('采样时刻', inplace=True)
    
    # 检查异常值
    power_col = '功率(kw)'
    anomaly_mask = df[power_col] < 0
    anomaly_count = anomaly_mask.sum()
    
    print(f"发现 {anomaly_count} 个异常值（功率<0）")
    print(f"异常值比例: {anomaly_count/len(df):.4f}")
    print(f"功率范围: {df[power_col].min():.2f} ~ {df[power_col].max():.2f}")
    
    # 处理异常值 - 用线性插值替换
    df.loc[anomaly_mask, power_col] = np.nan
    df[power_col] = df[power_col].interpolate(method='linear')
    df[power_col] = df[power_col].fillna(method='ffill').fillna(method='bfill')
    
    print(f"清洗后功率范围: {df[power_col].min():.2f} ~ {df[power_col].max():.2f}")
    
    return df

def create_sequences(data, sequence_length=24):
    """创建时间序列序列"""
    print(f"创建序列，序列长度: {sequence_length}")
    
    # 选择特征
    feature_cols = ['风速(m/s)', '功率(kw)', '发电机转速(rpm)', '叶片1角度(°)']
    data_features = data[feature_cols].values
    
    # 数据标准化
    scaler = MinMaxScaler()
    data_scaled = scaler.fit_transform(data_features)
    
    # 创建序列
    X, y = [], []
    for i in range(sequence_length, len(data_scaled)):
        X.append(data_scaled[i-sequence_length:i])  # 使用所有特征
        y.append(data_scaled[i, 1])  # 功率是第2列（索引1）
    
    X, y = np.array(X), np.array(y)
    
    # 划分训练集和测试集
    split_idx = int(len(X) * 0.8)
    X_train, X_test = X[:split_idx], X[split_idx:]
    y_train, y_test = y[:split_idx], y[split_idx:]
    
    print(f"训练集形状: X_train: {X_train.shape}, y_train: {y_train.shape}")
    print(f"测试集形状: X_test: {X_test.shape}, y_test: {y_test.shape}")
    
    return X_train, X_test, y_train, y_test, scaler

def build_lstm_model(input_shape):
    """构建LSTM模型"""
    print("=== 构建LSTM模型 ===")
    
    model = Sequential([
        LSTM(64, return_sequences=True, input_shape=input_shape),
        Dropout(0.2),
        LSTM(32, return_sequences=False),
        Dropout(0.2),
        Dense(50, activation='relu'),
        Dropout(0.3),
        Dense(1)
    ])
    
    model.compile(
        optimizer=Adam(learning_rate=0.001),
        loss='mse',
        metrics=['mae']
    )
    
    print("模型结构:")
    model.summary()
    
    return model

def train_model(model, X_train, y_train, X_test, y_test):
    """训练模型"""
    print("=== 训练模型 ===")
    
    # 设置回调函数
    callbacks = [
        EarlyStopping(
            monitor='val_loss',
            patience=15,
            restore_best_weights=True,
            verbose=1
        ),
        ReduceLROnPlateau(
            monitor='val_loss',
            factor=0.5,
            patience=8,
            min_lr=1e-7,
            verbose=1
        )
    ]
    
    # 训练模型
    history = model.fit(
        X_train, y_train,
        batch_size=32,
        epochs=100,
        validation_data=(X_test, y_test),
        callbacks=callbacks,
        verbose=1,
        shuffle=False
    )
    
    return history

def evaluate_model(model, X_test, y_test, scaler):
    """评估模型"""
    print("=== 模型评估 ===")
    
    # 预测
    y_pred_scaled = model.predict(X_test, verbose=0).flatten()
    
    # 反标准化
    # 创建虚拟数据进行反标准化
    dummy_data_test = np.zeros((len(y_test), 4))
    dummy_data_test[:, 1] = y_test
    y_test_original = scaler.inverse_transform(dummy_data_test)[:, 1]
    
    dummy_data_pred = np.zeros((len(y_pred_scaled), 4))
    dummy_data_pred[:, 1] = y_pred_scaled
    y_pred_original = scaler.inverse_transform(dummy_data_pred)[:, 1]
    
    # 计算评估指标
    mse = mean_squared_error(y_test_original, y_pred_original)
    rmse = np.sqrt(mse)
    mae = mean_absolute_error(y_test_original, y_pred_original)
    r2 = r2_score(y_test_original, y_pred_original)
    
    print(f"均方误差 (MSE): {mse:.2f}")
    print(f"均方根误差 (RMSE): {rmse:.2f}")
    print(f"平均绝对误差 (MAE): {mae:.2f}")
    print(f"决定系数 (R²): {r2:.4f}")
    
    return y_pred_original, y_test_original, {'mse': mse, 'rmse': rmse, 'mae': mae, 'r2': r2}

def plot_results(history, y_test, y_pred):
    """绘制结果"""
    print("=== 绘制结果 ===")
    
    fig, axes = plt.subplots(2, 3, figsize=(18, 10))
    
    # 训练历史 - 损失
    axes[0, 0].plot(history.history['loss'], label='训练损失')
    axes[0, 0].plot(history.history['val_loss'], label='验证损失')
    axes[0, 0].set_title('模型损失')
    axes[0, 0].set_xlabel('Epoch')
    axes[0, 0].set_ylabel('Loss')
    axes[0, 0].legend()
    
    # 训练历史 - MAE
    axes[0, 1].plot(history.history['mae'], label='训练MAE')
    axes[0, 1].plot(history.history['val_mae'], label='验证MAE')
    axes[0, 1].set_title('平均绝对误差')
    axes[0, 1].set_xlabel('Epoch')
    axes[0, 1].set_ylabel('MAE')
    axes[0, 1].legend()
    
    # 预测vs实际值散点图
    axes[0, 2].scatter(y_test, y_pred, alpha=0.6)
    axes[0, 2].plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--', lw=2)
    axes[0, 2].set_xlabel('实际功率 (kW)')
    axes[0, 2].set_ylabel('预测功率 (kW)')
    axes[0, 2].set_title('预测值 vs 实际值')
    
    # 时间序列对比图
    n_show = min(200, len(y_test))
    axes[1, 0].plot(range(n_show), y_test[-n_show:], label='实际值', alpha=0.8)
    axes[1, 0].plot(range(n_show), y_pred[-n_show:], label='预测值', alpha=0.8)
    axes[1, 0].set_xlabel('时间步')
    axes[1, 0].set_ylabel('功率 (kW)')
    axes[1, 0].set_title(f'时间序列预测对比（最后{n_show}个点）')
    axes[1, 0].legend()
    
    # 残差图
    residuals = y_test - y_pred
    axes[1, 1].scatter(y_pred, residuals, alpha=0.6)
    axes[1, 1].axhline(y=0, color='r', linestyle='--')
    axes[1, 1].set_xlabel('预测功率 (kW)')
    axes[1, 1].set_ylabel('残差')
    axes[1, 1].set_title('残差图')
    
    # 残差分布直方图
    axes[1, 2].hist(residuals, bins=50, alpha=0.7, edgecolor='black')
    axes[1, 2].set_xlabel('残差')
    axes[1, 2].set_ylabel('频次')
    axes[1, 2].set_title('残差分布')
    
    plt.tight_layout()
    plt.savefig('tensorflow_wind_turbine_results.png', dpi=300, bbox_inches='tight')
    plt.show()

def main():
    """主函数"""
    print("=== 基于TensorFlow的风机功率时间序列预测 ===")
    
    # 加载和预处理数据
    df = load_and_preprocess_data('2021年1月1期01风机数据.csv')
    
    # 创建序列
    X_train, X_test, y_train, y_test, scaler = create_sequences(df, sequence_length=24)
    
    # 构建模型
    model = build_lstm_model((X_train.shape[1], X_train.shape[2]))
    
    # 训练模型
    history = train_model(model, X_train, y_train, X_test, y_test)
    
    # 评估模型
    y_pred, y_test_orig, metrics = evaluate_model(model, X_test, y_test, scaler)
    
    # 绘制结果
    plot_results(history, y_test_orig, y_pred)
    
    print("\n=== 模型训练完成 ===")
    print("结果图片已保存为: tensorflow_wind_turbine_results.png")
    print(f"\n最终评估指标:")
    print(f"RMSE: {metrics['rmse']:.2f} kW")
    print(f"MAE: {metrics['mae']:.2f} kW") 
    print(f"R²: {metrics['r2']:.4f}")
    
    # 保存模型
    model.save('wind_turbine_lstm_model.h5')
    print("模型已保存为: wind_turbine_lstm_model.h5")

if __name__ == "__main__":
    main()
